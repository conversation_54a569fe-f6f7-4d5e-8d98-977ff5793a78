System Architecture 

The SmartVA system implements Retrieval Augmented Generation (RAG) entirely on-device, eliminating the need for cloud-based inference. The architecture, shown in Figure 1, leverages a modular, layered design integrating React Native's cross-platform capabilities with high-performance native libraries. 

![High-Level System Architecture of Mobile Edge RAG Deployment. The system operates entirely within the device security boundary, eliminating external data transmission. The React Native application layer manages user interactions and coordinates between the LLM inference engine (utilizing LLaMA.rn with GGUF models) and the RAG pipeline (featuring ONNX embeddings, BERT-style tokenization, and HNSW vector search). All components access local storage for models, indices, and document chunks, with performance-critical operations handled through native Rust bridges. The numbered arrows indicate the primary data flow: (1) user query input, (2) query processing through the RAG pipeline, (3) retrieved context injection into the LLM, and (4) generated response delivery.](diagrams/system_architecture.svg)

4.1 Architectural Overview 

The architecture comprises five distinct layers: the mobile application layer, core application logic, AI/ML processing components, native bridge integration, and high-performance native libraries. This design optimizes computational efficiency through native code while maintaining React Native's development speed and cross-platform compatibility. The complete RAG workflow includes query embedding with ONNX models, vector retrieval using Rust-based HNSW indexing, prompt augmentation with retrieved contexts, and quantized LLM inference through llama.rn, ensuring data privacy and offline functionality. 

4.2 Mobile Application Layer 

Picture 

Built using React Native, the unified mobile interface provides a responsive chat experience with streaming capabilities, model management, and real-time RAG system monitoring. Users can dynamically download quantized GGUF models from Hugging Face repositories optimized for mobile. Key features include adaptive document retrieval, performance metrics (tokens-per-second, time-to-first-token), and comprehensive debug capabilities, ensuring responsiveness during intensive AI operations via asynchronous processing and optimized threading. 

4.3 Core Application Logic 

The core logic coordinates interactions between AI components, native libraries, and the user interface. The central RAGService class manages embedding initialization, vector search, document retrieval, and caching mechanisms, including metadata chunk caching and smart prefetching. The ModelConfig module intelligently determines optimal chunk sizes and validates RAG compatibility, while RAGUtils performs relevance ranking combining vector similarity and semantic content analysis, adjusting chunk counts dynamically based on query complexity. 

4.4 AI/ML Processing Components 

The system employs two complementary AI models: an ONNX embedding model (all-MiniLM-L6-v2) for semantic search, generating optimized 384-dimensional vectors, and a quantized LLM via llama.rn for text generation. Embedding operations run via ONNX Runtime, separate from LLM memory space, enabling simultaneous loading and minimal latency (See Figure 2). The LLM component supports various quantization levels (4-bit, 5-bit, 8-bit) and GGUF model formats, with dynamic loading, hot-swapping, and real-time token streaming for inference. However, as the number of retrieved documents increases, noticeable degradation in time-to-first-token (TTFT) and tokens per second occurs. 

![RAG Processing Pipeline Technical Detail. The pipeline processes user queries through five distinct stages: (1) adaptive preprocessing that determines optimal document retrieval count based on query complexity and the selected model's context window size, (2) dual-tokenizer strategy with local BERT vocabulary or bundled fallback, (3) quantized ONNX embedding generation with attention-weighted pooling, (4) native Rust HNSW vector search with memory-mapped indices achieving sub-5ms performance, and (5) efficient document loading with LRU caching and relevance ranking for context formation.](diagrams/rag.svg)


4.5 Native Bridge Integration 

The native bridge enables efficient JavaScript-native communication with dedicated modules for iOS (Swift/Objective-C) and Android (Java/Kotlin). The bridges provide access to vector search and asset management, minimizing serialization overhead through optimized memory access patterns, ensuring native performance benefits translate effectively into application responsiveness. 

![Native Bridge Architecture for Cross-Platform Rust Integration. The system employs a unified React Native bridge interface that routes to platform-specific implementations: iOS uses direct Objective-C bindings with C header integration (HnswSearch.h) linking to a static Rust library (libhnsw_lib.a), while Android requires an additional JNI layer (hnsw_jni.cpp) compiled into a shared object (libhnsw_native.so) before accessing the same static Rust core. Both platforms converge on identical Rust FFI functions (hnsw_search()) ensuring consistent cross-platform behavior while accommodating platform-specific integration requirements. The architecture demonstrates how a single Rust codebase can provide high-performance native functionality across mobile platforms through appropriate abstraction layers.](diagrams/native_bridge.svg)

4.6 High-Performance Native Libraries 

High-performance native libraries, primarily implemented in Rust, underpin vector retrieval operations. The hnsw_lib employs Hierarchical Navigable Small World indexing for rapid approximate nearest neighbor searches, optimized for mobile hardware through memory-mapped file access and concurrent searching. The hnsw_builder component constructs optimized indices offline, incorporating compression and memory layout optimization, compiled specifically for iOS (static) and Android (shared libraries). 

4.7 Data Processing Pipeline 

The Python-based offline pipeline prepares datasets from PDFs, Excel files, and plain texts, employing intelligent extraction and semantic-aware chunking strategies. Using identical ONNX embedding models for consistency with runtime operations, the pipeline balances chunk context and retrieval granularity (200-500 tokens per chunk), preserving semantic coherence through chunk overlap. 

![Document Processing and Storage Architecture. The system employs a three-phase approach: (1) offline processing through embed_docs.py that ingests multiple document formats, applies intelligent sentence-aware chunking (1000 characters with 200-character overlap), generates 384-dimensional embeddings using MiniLM-L6-v2, and builds HNSW indices via custom Rust CLI with optimized parameters (M=16, ef\_construction=200); (2) storage architecture featuring chunked metadata system with 1000 documents per 5MB file for mobile memory constraints, memory-mapped hnsw index files (.data/.graph), and automated cross-platform asset distribution; and (3) runtime memory management using LRU cache system (8-chunk maximum, ~40MB) with intelligent O(1) document loading via floor(docID/1000) formula, achieving 85% cache hit rates and 2-5ms load times with automatic prefetching of initial chunks.](diagrams/data_prep_pipeline.svg)

 
4.8 Performance Characteristics and Optimizations 

The architecture achieves reasonable performance through optimized memory management, model unloading, and hot-swapping capabilities. The vector search typically achieves sub-second retrieval times with optimized HNSW parameters and efficient memory use, though retrieval performance declines with increased document count. Batch processing optimizes embedding operations, reducing repeated initialization overhead. Quantized LLM inference performance varies significantly with document retrieval volume, typically achieving between 5-20 tokens per second on current mobile hardware, depending on query complexity and retrieval size. 

![Memory Allocation for Parallel LLM and Embedding Model Execution. The system manages concurrent operation of two AI models within mobile memory constraints: the LLM inference engine consumes 70-90% of available memory (1.3-4GB) through quantized GGUF model weights, dynamic context buffers, and generation state, while the embedding model operates efficiently within 5-10% (∼80MB) using a quantized ONNX model, tokenizer, and LRU document cache. Shared resources including memory-mapped HNSW indices and application runtime comprise the remaining allocation. The architecture employs adaptive memory management strategies including dynamic context window scaling and cache eviction to handle memory pressure, enabling practical deployment of edge RAG systems on resource-constrained mobile devices where traditional cloud-based approaches would be prohibitive.](diagrams/memory_management.svg)

4.9 Technical Innovation and Contributions 

The SmartVA system integrates high-performance HNSW vector search and optimized ONNX embeddings directly onto resource-constrained mobile hardware. Its multi-model orchestration demonstrates memory and computational management strategies suitable for mobile environments. This modular and scalable design provides insights into privacy-preserving, fully offline AI applications on mobile platforms. 