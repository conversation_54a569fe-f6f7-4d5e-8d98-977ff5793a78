System Architecture 

The SmartVA system implements Retrieval Augmented Generation (RAG) entirely on-device, eliminating the need for cloud-based inference. The architecture, shown in Figure 1, leverages a modular, layered design integrating React Native's cross-platform capabilities with high-performance native libraries. 

![High-Level System Architecture of Mobile Edge RAG Deployment. The system operates entirely within the device security boundary, eliminating external data transmission. The React Native application layer manages user interactions and coordinates between the LLM inference engine (utilizing LLaMA.rn with GGUF models) and the RAG pipeline (featuring ONNX embeddings, BERT-style tokenization, and HNSW vector search). All components access local storage for models, indices, and document chunks, with performance-critical operations handled through native Rust bridges. The numbered arrows indicate the primary data flow: (1) user query input, (2) query processing through the RAG pipeline, (3) retrieved context injection into the LLM, and (4) generated response delivery.](diagrams/system_architecture.svg)

4.1 Architectural Overview

The architecture comprises four distinct layers: (1) Mobile Application Layer (React Native), (2) LLM Inference Layer, (3) RAG Processing Layer (JavaScript + Native Modules), and (4) High-Performance Native Layer (Rust + Platform Bridges). This design optimizes computational efficiency through native code while maintaining React Native's development speed and cross-platform compatibility. The complete RAG workflow includes query embedding with ONNX models, vector retrieval using Rust-based HNSW indexing, prompt augmentation with retrieved contexts, and quantized LLM inference through llama.rn, ensuring data privacy and offline functionality.

4.2 Mobile Application Layer 

Picture 

Built using React Native, the unified mobile interface provides a responsive chat experience with streaming capabilities, model management, and real-time RAG system monitoring. Users can dynamically download quantized GGUF models from Hugging Face repositories optimized for mobile. Key features include adaptive document retrieval, performance metrics (tokens-per-second, time-to-first-token), and comprehensive debug capabilities, ensuring responsiveness during intensive AI operations via asynchronous processing and optimized threading. 

4.3 LLM Inference Layer

The LLM inference layer operates as a dedicated computational engine for text generation, consuming 70-90% of available device memory (1.3-4GB) through quantized GGUF model weights and dynamic context buffers. Built on llama.rn, this layer supports various quantization levels (4-bit, 5-bit, 8-bit) with dynamic model loading, hot-swapping capabilities, and real-time token streaming. The layer integrates seamlessly with the RAG pipeline through context injection, where retrieved documents are incorporated into prompts before generation. Performance characteristics include 5-20 tokens per second on mobile hardware, with noticeable degradation in time-to-first-token (TTFT) as retrieved document count increases, requiring careful balance between context richness and inference speed.

4.4 RAG Processing Layer (JavaScript + Native Modules)

The RAG processing layer orchestrates intelligent document retrieval and context formation through coordinated JavaScript services and native module integration. The central RAGService class manages the complete retrieval pipeline: ONNX embedding generation using all-MiniLM-L6-v2 (384-dimensional vectors), BERT-style tokenization with local vocabulary fallback, and vector similarity search coordination. This layer implements sophisticated caching mechanisms including metadata chunk caching (LRU with 8-chunk maximum), smart prefetching strategies, and relevance ranking that combines vector similarity with semantic content analysis. The ModelConfig and RAGUtils modules provide intelligent chunk size determination and dynamic retrieval count adjustment based on query complexity and model context windows, ensuring optimal performance across different LLM configurations.

![RAG Processing Pipeline Technical Detail. The pipeline processes user queries through five distinct stages: (1) adaptive preprocessing that determines optimal document retrieval count based on query complexity and the selected model's context window size, (2) dual-tokenizer strategy with local BERT vocabulary or bundled fallback, (3) quantized ONNX embedding generation with attention-weighted pooling, (4) native Rust HNSW vector search with memory-mapped indices achieving sub-5ms performance, and (5) efficient document loading with LRU caching and relevance ranking for context formation.](diagrams/rag.svg)


4.5 High-Performance Native Layer (Rust + Platform Bridges)

The high-performance native layer provides computationally intensive vector search operations through a unified Rust codebase with platform-specific integration bridges. The core hnsw_lib implements Hierarchical Navigable Small World indexing for rapid approximate nearest neighbor searches, achieving sub-5ms performance through memory-mapped file access and optimized search parameters (M=16, ef_construction=200). The system employs a sophisticated cross-platform architecture: iOS uses direct Objective-C bindings with C header integration linking to a static Rust library (libhnsw_lib.a), while Android requires an additional JNI layer compiled into a shared object (libhnsw_native.so). Both platforms converge on identical Rust FFI functions ensuring consistent behavior while accommodating platform-specific integration requirements. The hnsw_builder component constructs optimized indices offline with compression and memory layout optimization, enabling efficient mobile deployment of large-scale vector databases.

![Native Bridge Architecture for Cross-Platform Rust Integration. The system employs a unified React Native bridge interface that routes to platform-specific implementations: iOS uses direct Objective-C bindings with C header integration (HnswSearch.h) linking to a static Rust library (libhnsw_lib.a), while Android requires an additional JNI layer (hnsw_jni.cpp) compiled into a shared object (libhnsw_native.so) before accessing the same static Rust core. Both platforms converge on identical Rust FFI functions (hnsw_search()) ensuring consistent cross-platform behavior while accommodating platform-specific integration requirements. The architecture demonstrates how a single Rust codebase can provide high-performance native functionality across mobile platforms through appropriate abstraction layers.](diagrams/native_bridge.svg)

4.6 Data Processing Pipeline

The Python-based offline pipeline prepares datasets from PDFs, Excel files, and plain texts, employing intelligent extraction and semantic-aware chunking strategies. Using identical ONNX embedding models for consistency with runtime operations, the pipeline balances chunk context and retrieval granularity (200-500 tokens per chunk), preserving semantic coherence through chunk overlap. 

![Document Processing and Storage Architecture. The system employs a three-phase approach: (1) offline processing through embed_docs.py that ingests multiple document formats, applies intelligent sentence-aware chunking (1000 characters with 200-character overlap), generates 384-dimensional embeddings using MiniLM-L6-v2, and builds HNSW indices via custom Rust CLI with optimized parameters (M=16, ef\_construction=200); (2) storage architecture featuring chunked metadata system with 1000 documents per 5MB file for mobile memory constraints, memory-mapped hnsw index files (.data/.graph), and automated cross-platform asset distribution; and (3) runtime memory management using LRU cache system (8-chunk maximum, ~40MB) with intelligent O(1) document loading via floor(docID/1000) formula, achieving 85% cache hit rates and 2-5ms load times with automatic prefetching of initial chunks.](diagrams/data_prep_pipeline.svg)

 
4.7 Performance Characteristics and Optimizations

The architecture achieves reasonable performance through optimized memory management, model unloading, and hot-swapping capabilities. The vector search typically achieves sub-second retrieval times with optimized HNSW parameters and efficient memory use, though retrieval performance declines with increased document count. Batch processing optimizes embedding operations, reducing repeated initialization overhead. Quantized LLM inference performance varies significantly with document retrieval volume, typically achieving between 5-20 tokens per second on current mobile hardware, depending on query complexity and retrieval size. 

![Memory Allocation for Parallel LLM and Embedding Model Execution. The system manages concurrent operation of two AI models within mobile memory constraints: the LLM inference engine consumes 70-90% of available memory (1.3-4GB) through quantized GGUF model weights, dynamic context buffers, and generation state, while the embedding model operates efficiently within 5-10% (∼80MB) using a quantized ONNX model, tokenizer, and LRU document cache. Shared resources including memory-mapped HNSW indices and application runtime comprise the remaining allocation. The architecture employs adaptive memory management strategies including dynamic context window scaling and cache eviction to handle memory pressure, enabling practical deployment of edge RAG systems on resource-constrained mobile devices where traditional cloud-based approaches would be prohibitive.](diagrams/memory_management.svg)

4.9 Technical Innovation and Contributions 

The SmartVA system integrates high-performance HNSW vector search and optimized ONNX embeddings directly onto resource-constrained mobile hardware. Its multi-model orchestration demonstrates memory and computational management strategies suitable for mobile environments. This modular and scalable design provides insights into privacy-preserving, fully offline AI applications on mobile platforms. 