<svg width="1000" height="600" viewBox="0 0 1000 600" xmlns="http://www.w3.org/2000/svg">
  <style>
    text, tspan {
      font-family: "Times New Roman";
    }
  </style>
  <!-- Background -->
  <rect width="1000" height="600" fill="#FFFFFF"/>
  <!-- Mobile Device Memory Container -->
  <rect x="50" y="40" width="900" height="520" fill="none" stroke="#333" stroke-width="3" rx="15"/>
  <text x="70" y="70" font-size="20" font-weight="bold" fill="#333">MOBILE DEVICE MEMORY (~1-4GB Available to App)</text>
  <!-- LLM Memory Block -->
  <rect x="80" y="100" width="380" height="380" fill="#ff6b6b" fill-opacity="0.15" stroke="#dc3545" stroke-width="2" rx="10"/>
  <text x="270" y="130" text-anchor="middle" font-size="20" font-weight="bold" fill="#8B0000">LLM INFERENCE ENGINE</text>
  <!-- Model Weights -->
  <rect x="100" y="150" width="340" height="90" fill="#ff6b6b" fill-opacity="0.4" stroke="#dc3545" stroke-width="2" rx="8"/>
  <text x="270" y="175" text-anchor="middle" font-size="16" font-weight="bold">Model Weights (GGUF)</text>
  <text x="180" y="200" text-anchor="middle" font-size="14">1B Model: ~1.2GB</text>
  <text x="360" y="200" text-anchor="middle" font-size="14">3B Model: ~3.5GB</text>
  <text x="270" y="220" text-anchor="middle" font-size="14">Quantized • Read-only • Memory-mapped</text>
  <!-- Context Buffer -->
  <rect x="100" y="260" width="160" height="80" fill="#ff6b6b" fill-opacity="0.3" stroke="#dc3545" stroke-width="1" rx="8"/>
  <text x="180" y="285" text-anchor="middle" font-size="16" font-weight="bold">Context Buffer</text>
  <text x="180" y="305" text-anchor="middle" font-size="13">2k tokens: ~16MB</text>
  <text x="180" y="320" text-anchor="middle" font-size="13">32k tokens: ~256MB</text>
  <text x="180" y="335" text-anchor="middle" font-size="13">Dynamic allocation</text>
  <!-- KV Cache -->
  <rect x="280" y="260" width="160" height="80" fill="#ff6b6b" fill-opacity="0.3" stroke="#dc3545" stroke-width="1" rx="8"/>
  <text x="360" y="285" text-anchor="middle" font-size="16" font-weight="bold">KV Cache</text>
  <text x="360" y="305" text-anchor="middle" font-size="13">Attention states</text>
  <text x="360" y="320" text-anchor="middle" font-size="13">~50-200MB</text>
  <text x="360" y="335" text-anchor="middle" font-size="13">Generation state</text>
  <!-- Generation Buffers -->
  <rect x="100" y="360" width="340" height="60" fill="#ff6b6b" fill-opacity="0.2" stroke="#dc3545" stroke-width="1" rx="8"/>
  <text x="270" y="385" text-anchor="middle" font-size="16" font-weight="bold">Generation Buffers</text>
  <text x="270" y="405" text-anchor="middle" font-size="13">Token processing • Temporary arrays • ~20-50MB</text>
  <!-- Total LLM Memory -->
  <rect x="100" y="440" width="340" height="30" fill="#dc3545" fill-opacity="0.6" stroke="#8B0000" stroke-width="2" rx="5"/>
  <text x="270" y="460" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Total: 1.3-4GB (70-90% of available memory)</text>
  <!-- Embedding Model Memory Block -->
  <rect x="500" y="150" width="280" height="330" fill="#4ecdc4" fill-opacity="0.15" stroke="#17a2b8" stroke-width="2" rx="10"/>
  <text x="640" y="180" text-anchor="middle" font-size="20" font-weight="bold" fill="#006064">EMBEDDING MODEL</text>
  <!-- ONNX Model -->
  <rect x="520" y="200" width="240" height="60" fill="#4ecdc4" fill-opacity="0.4" stroke="#17a2b8" stroke-width="2" rx="8"/>
  <text x="640" y="225" text-anchor="middle" font-size="16" font-weight="bold">ONNX Model Weights</text>
  <text x="640" y="245" text-anchor="middle" font-size="14">MiniLM-L6-v2 quantized: ~25MB</text>
  <!-- Tokenizer -->
  <rect x="520" y="280" width="110" height="50" fill="#4ecdc4" fill-opacity="0.3" stroke="#17a2b8" stroke-width="1" rx="8"/>
  <text x="575" y="300" text-anchor="middle" font-size="14" font-weight="bold">Tokenizer</text>
  <text x="575" y="315" text-anchor="middle" font-size="12">Vocab: ~5MB</text>
  <!-- Runtime Buffers -->
  <rect x="650" y="280" width="110" height="50" fill="#4ecdc4" fill-opacity="0.3" stroke="#17a2b8" stroke-width="1" rx="8"/>
  <text x="705" y="300" text-anchor="middle" font-size="14" font-weight="bold">Runtime</text>
  <text x="705" y="315" text-anchor="middle" font-size="12">Buffers: ~10MB</text>
  <!-- Document Cache -->
  <rect x="520" y="350" width="240" height="60" fill="#4ecdc4" fill-opacity="0.3" stroke="#17a2b8" stroke-width="1" rx="8"/>
  <text x="640" y="375" text-anchor="middle" font-size="16" font-weight="bold">Document Cache (LRU)</text>
  <text x="640" y="395" text-anchor="middle" font-size="14">8 chunks × 5MB = ~40MB maximum</text>
  <!-- Total Embedding Memory -->
  <rect x="520" y="430" width="240" height="30" fill="#17a2b8" fill-opacity="0.6" stroke="#006064" stroke-width="2" rx="5"/>
  <text x="640" y="450" text-anchor="middle" font-size="16" font-weight="bold" fill="white">Total: ~80MB (5-10% of memory)</text>
  <!-- Shared Resources -->
  <rect x="800" y="100" width="120" height="290" fill="#ffd93d" fill-opacity="0.2" stroke="#f39c12" stroke-width="2" rx="10"/>
  <text x="860" y="130" text-anchor="middle" font-size="16" font-weight="bold" fill="#B8860B">SHARED</text>
  <text x="860" y="145" text-anchor="middle" font-size="16" font-weight="bold" fill="#B8860B">RESOURCES</text>
  <!-- HNSW Index -->
  <rect x="810" y="160" width="100" height="60" fill="#ffd93d" fill-opacity="0.4" stroke="#f39c12" stroke-width="1" rx="8"/>
  <text x="860" y="180" text-anchor="middle" font-size="14" font-weight="bold">HNSW Index</text>
  <text x="860" y="195" text-anchor="middle" font-size="12">Memory-mapped</text>
  <text x="860" y="210" text-anchor="middle" font-size="12">~10-50MB</text>
  <!-- File Buffers -->
  <rect x="810" y="240" width="100" height="60" fill="#ffd93d" fill-opacity="0.4" stroke="#f39c12" stroke-width="1" rx="8"/>
  <text x="860" y="260" text-anchor="middle" font-size="14" font-weight="bold">File I/O</text>
  <text x="860" y="275" text-anchor="middle" font-size="12">OS buffers</text>
  <text x="860" y="290" text-anchor="middle" font-size="12">~20-30MB</text>
  <!-- App Runtime -->
  <rect x="810" y="320" width="100" height="60" fill="#ffd93d" fill-opacity="0.4" stroke="#f39c12" stroke-width="1" rx="8"/>
  <text x="860" y="340" text-anchor="middle" font-size="14" font-weight="bold">App Runtime</text>
  <text x="860" y="355" text-anchor="middle" font-size="12">React Native</text>
  <text x="860" y="370" text-anchor="middle" font-size="12">~100-200MB</text>
  <!-- Memory Management Strategy -->
  <rect x="80" y="500" width="840" height="50" fill="#e74c3c" fill-opacity="0.2" stroke="#c0392b" stroke-width="2" rx="10"/>
  <text x="500" y="520" text-anchor="middle" font-size="18" font-weight="bold" fill="#8B0000">MEMORY PRESSURE STRATEGY</text>
  <text x="500" y="540" text-anchor="middle" font-size="14" fill="#8B0000">LLM: Reduce context window • RAG: Clear document cache • Critical: Fallback to smaller model</text>
  <!-- Memory Isolation Indicators -->
  <path d="M 480 250 L 480 250" stroke="none"/>
  <text x="480" y="250" font-size="16" font-weight="bold" fill="#666">|</text>
  <text x="480" y="270" font-size="16" font-weight="bold" fill="#666">|</text>
  <text x="480" y="290" font-size="16" font-weight="bold" fill="#666">|</text>
  <text x="480" y="310" font-size="16" font-weight="bold" fill="#666">|</text>
  <text x="480" y="330" font-size="16" font-weight="bold" fill="#666">|</text>
  <!-- Memory Flow Arrows -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
  </defs>
  <!-- Shared resource arrows -->
  <path d="M 780 175 L 800 175" stroke="#666" stroke-width="1" marker-end="url(#arrowhead)"/>
  <path d="M 460 125 L 800 125" stroke="#666" stroke-width="1" marker-end="url(#arrowhead)"/>
</svg>